import { useState } from 'react'
import { useTheme } from '@/context/theme-context'
import { useBranchTerminalActivityHeatmap } from '@/hooks/use-branches'
import type { DateFilters } from '@/types/api'
import { Button } from '@/components/ui/button'
import { ChevronLeft, ChevronRight } from 'lucide-react'

interface BranchTerminalActivityHeatmapProps {
  branchId: string
  granularity?: 'daily' | 'weekly' | 'monthly' | 'yearly'
  dateFilters?: DateFilters
  mode?: 'volume' | 'count' | 'average'
}

interface HeatmapData {
  metric: string
  periods: string[]
  transaction_volume: Array<{ terminal: string; [key: string]: any }>
  transaction_count: Array<{ terminal: string; [key: string]: any }>
  average_transaction_value: Array<{ terminal: string; [key: string]: any }>
}

export function BranchTerminalActivityHeatmap({ 
  branchId, 
  granularity = 'monthly', 
  dateFilters = {}, 
  mode = 'volume' 
}: BranchTerminalActivityHeatmapProps) {
  const { theme } = useTheme()
  const [currentPage, setCurrentPage] = useState(0)
  const itemsPerPage = 10

  const { data, isLoading, error } = useBranchTerminalActivityHeatmap(
    branchId,
    { granularity, ...dateFilters },
    !!branchId
  )

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-[300px]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-[300px] text-muted-foreground">
        <p>Error loading heatmap data</p>
      </div>
    )
  }

  if (!data || !data.periods || data.periods.length === 0) {
    return (
      <div className="flex items-center justify-center h-[300px] text-muted-foreground">
        <p>No heatmap data available for the selected period</p>
      </div>
    )
  }

  const heatmapData = data as HeatmapData
  const periods = heatmapData.periods || []
  
  // Get the appropriate data based on mode
  let terminalData: Array<{ terminal: string; [key: string]: any }> = []
  if (mode === 'volume') {
    terminalData = heatmapData.transaction_volume || []
  } else if (mode === 'count') {
    terminalData = heatmapData.transaction_count || []
  } else {
    terminalData = heatmapData.average_transaction_value || []
  }

  if (terminalData.length === 0) {
    return (
      <div className="flex items-center justify-center h-[300px] text-muted-foreground">
        <p>No terminal activity data available</p>
      </div>
    )
  }

  // Calculate pagination
  const totalPages = Math.ceil(terminalData.length / itemsPerPage)
  const startIndex = currentPage * itemsPerPage
  const endIndex = startIndex + itemsPerPage
  const paginatedData = terminalData.slice(startIndex, endIndex)

  // Calculate color intensity based on values
  const allValues = terminalData.flatMap(terminal => 
    periods.map(period => terminal[period] || 0)
  ).filter(val => val > 0)
  
  const maxValue = Math.max(...allValues, 1)
  const minValue = Math.min(...allValues.filter(val => val > 0), 0)

  const getColorIntensity = (value: number) => {
    if (value === 0) return 'transparent'
    const intensity = (value - minValue) / (maxValue - minValue)
    const opacity = Math.max(0.1, intensity)
    return theme === 'dark' 
      ? `rgba(8, 81, 138, ${opacity})` 
      : `rgba(8, 81, 138, ${opacity})`
  }

  const formatValue = (value: number) => {
    if (mode === 'volume') {
      return `₵${value.toLocaleString()}`
    } else if (mode === 'count') {
      return value.toLocaleString()
    } else {
      return `₵${value.toLocaleString()}`
    }
  }

  const colors = {
    text: theme === 'dark' ? '#ffffff' : '#000000',
    border: theme === 'dark' ? '#374151' : '#e5e7eb'
  }

  return (
    <div className="space-y-4">
      {/* Pagination Controls */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between">
          <p className="text-sm text-muted-foreground">
            Showing {startIndex + 1}-{Math.min(endIndex, terminalData.length)} of {terminalData.length} terminals
          </p>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(prev => Math.max(0, prev - 1))}
              disabled={currentPage === 0}
            >
              <ChevronLeft className="h-4 w-4" />
              Previous
            </Button>
            <span className="text-sm">
              Page {currentPage + 1} of {totalPages}
            </span>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(prev => Math.min(totalPages - 1, prev + 1))}
              disabled={currentPage === totalPages - 1}
            >
              Next
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}

      {/* Heatmap */}
      <div className="overflow-x-auto">
        <div className="min-w-fit">
          {/* Header */}
          <div 
            className="grid gap-1 mb-2"
            style={{ gridTemplateColumns: `200px repeat(${periods.length}, 80px)` }}
          >
            <div className="p-2 text-xs font-semibold bg-muted rounded">
              Terminal
            </div>
            {periods.map((period) => (
              <div key={period} className="p-2 text-xs font-semibold text-center bg-muted rounded">
                {period}
              </div>
            ))}
          </div>

          {/* Data rows */}
          <div className="space-y-1">
            {paginatedData.map((terminal, index) => (
              <div
                key={terminal.terminal}
                className="grid gap-1"
                style={{ gridTemplateColumns: `200px repeat(${periods.length}, 80px)` }}
              >
                <div className="p-2 text-xs font-medium truncate bg-muted/50 rounded">
                  {terminal.terminal}
                </div>
                {periods.map((period) => {
                  const value = terminal[period] || 0
                  return (
                    <div
                      key={period}
                      className="p-2 text-xs text-center rounded transition-colors"
                      style={{
                        backgroundColor: getColorIntensity(value),
                        color: value > 0 ? colors.text : 'hsl(var(--muted-foreground))'
                      }}
                      title={`${terminal.terminal} - ${period}: ${formatValue(value)}`}
                    >
                      {value > 0 ? formatValue(value) : '-'}
                    </div>
                  )
                })}
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Legend */}
      <div className="flex items-center gap-4 text-xs text-muted-foreground">
        <span>Activity Level:</span>
        <div className="flex items-center gap-2">
          <div className="w-4 h-4 rounded" style={{ backgroundColor: 'transparent', border: '1px solid hsl(var(--border))' }}></div>
          <span>None</span>
        </div>
        <div className="flex items-center gap-2">
          <div className="w-4 h-4 rounded" style={{ backgroundColor: getColorIntensity(minValue) }}></div>
          <span>Low</span>
        </div>
        <div className="flex items-center gap-2">
          <div className="w-4 h-4 rounded" style={{ backgroundColor: getColorIntensity(maxValue) }}></div>
          <span>High</span>
        </div>
      </div>
    </div>
  )
}

import { useState } from 'react'
import { useParams } from '@tanstack/react-router'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { ArrowLeft, Monitor } from 'lucide-react'
import { useNavigate } from '@tanstack/react-router'
import { DateFilters } from '@/components/date-filters'
import { ChannelFilter } from '@/components/channel-filter'
import { ErrorBoundary } from '@/components/error-boundary'
import { ModeSelector } from '@/components/mode-selector'
import { GranularitySelector } from '@/components/granularity-selector'
import type { DateFilters as DateFiltersType } from '@/types/api'

// Import terminal dashboard components
import { TerminalOverview } from './components/terminal-overview'
import { TerminalTopCustomers } from './components/terminal-top-customers'
import { TerminalTransactionFrequencyAnalysis } from './components/terminal-transaction-frequency-analysis'
import { TerminalTransactionVolume } from './components/terminal-transaction-volume'
import { TerminalStats } from './components/terminal-stats'

const STATIC_LIMIT = 5

export function TerminalDashboard() {
  const { merchantId, branchId, terminalId } = useParams({ strict: false })
  const navigate = useNavigate()

  // State for filters and modes
  const [dateFilters, setDateFilters] = useState<DateFiltersType>({})
  const [channelFilter, setChannelFilter] = useState<string>('')
  const [customersMode, setCustomersMode] = useState<'amount' | 'count'>('amount')
  const [granularity, setGranularity] = useState<'daily' | 'weekly' | 'monthly' | 'yearly'>('monthly')

  if (!terminalId) {
    return (
      <div className="container mx-auto p-6">
        <div className="text-center py-8">
          <p className="text-destructive">Terminal ID is required</p>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate({ to: `/merchants/${merchantId}/${branchId}` })}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Branch
          </Button>
          <div className="flex items-center gap-3">
            <div className="h-10 w-10 rounded-lg bg-primary/10 flex items-center justify-center">
              <Monitor className="h-5 w-5 text-primary" />
            </div>
            <div>
              <h1 className="text-2xl font-bold">{terminalId}</h1>
              <p className="text-muted-foreground">
                Terminal analytics and insights
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Filters</CardTitle>
          <CardDescription>
            Filter data by date range and transaction channel
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-4">
            <div className="flex-1 min-w-[300px]">
              <DateFilters
                value={dateFilters}
                onChange={setDateFilters}
              />
            </div>
            <div className="min-w-[200px]">
              <ChannelFilter
                value={channelFilter}
                onChange={setChannelFilter}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Dashboard Content */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        {/* Overview Stats - Full Width */}
        <div className="col-span-full">
          <ErrorBoundary>
            <TerminalOverview
              terminalId={terminalId}
              dateFilters={dateFilters}
              channelFilter={channelFilter}
            />
          </ErrorBoundary>
        </div>

        {/* Transaction Volume Chart - Full Width */}
        <Card className="col-span-full">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>Transaction Trend Over Time</CardTitle>
                <CardDescription>
                  {granularity.charAt(0).toUpperCase() + granularity.slice(1)} transaction patterns for this terminal
                </CardDescription>
              </div>
              <div className="flex items-center gap-2 flex-shrink-0">
                <ModeSelector
                  value={customersMode}
                  onValueChange={setCustomersMode}
                  label="Mode"
                  className="min-w-[120px]"
                />
                <GranularitySelector
                  value={granularity}
                  onValueChange={setGranularity}
                  label="Granularity"
                  className="min-w-[120px]"
                />
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <ErrorBoundary>
              <TerminalTransactionVolume
                terminalId={terminalId}
                granularity={granularity}
                mode={customersMode}
                dateFilters={dateFilters}
                channelFilter={channelFilter}
              />
            </ErrorBoundary>
          </CardContent>
        </Card>

        {/* Top Customers - Half Width */}
        <Card className="col-span-full md:col-span-1 lg:col-span-2">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>Top 5 Customers</CardTitle>
                <CardDescription>
                  Customers with highest {customersMode === 'amount' ? 'transaction value' : 'transaction count'} at this terminal
                </CardDescription>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <ErrorBoundary>
              <TerminalTopCustomers
                terminalId={terminalId}
                mode={customersMode}
                limit={STATIC_LIMIT}
                dateFilters={dateFilters}
                channelFilter={channelFilter}
              />
            </ErrorBoundary>
          </CardContent>
        </Card>

        {/* Terminal Stats - Half Width */}
        <Card className="col-span-full md:col-span-1 lg:col-span-2">
          <CardHeader>
            <CardTitle>Terminal Statistics</CardTitle>
            <CardDescription>
              Key performance metrics for this terminal
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ErrorBoundary>
              <TerminalStats
                terminalId={terminalId}
                dateFilters={dateFilters}
                channelFilter={channelFilter}
              />
            </ErrorBoundary>
          </CardContent>
        </Card>

        {/* Transaction Frequency Analysis - Full Width */}
        <div className="col-span-full">
          <ErrorBoundary>
            <TerminalTransactionFrequencyAnalysis
              terminalId={terminalId}
              dateFilters={dateFilters}
              channelFilter={channelFilter}
            />
          </ErrorBoundary>
        </div>
      </div>
    </div>
  )
}

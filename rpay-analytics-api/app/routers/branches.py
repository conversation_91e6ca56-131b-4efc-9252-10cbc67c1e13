from fastapi import APIRouter, HTTPException, Query, Depends
from typing import Optional, List
import pandas as pd

from app.core.data import get_df
from app.logic import branch_admins
from app.models.stats import SimpleStat, GraphData, TableData
from app.utils.analytics import _apply_date_filters

router = APIRouter(prefix="/branches", tags=["branches"])

def get_branch_data(branch_id: str, filters: dict = None) -> pd.DataFrame:
    """Get filtered data for a specific branch."""
    df = get_df()
    
    # Filter by branch_admin_id
    branch_df = df[df['branch_admin_id'] == branch_id].copy()
    
    if branch_df.empty:
        raise HTTPException(status_code=404, detail=f"No data found for branch {branch_id}")
    
    # Apply additional filters
    if filters:
        branch_df = _apply_date_filters(
            branch_df,
            year=filters.get('year'),
            month=filters.get('month'),
            week=filters.get('week'),
            day=filters.get('day'),
            range_days=filters.get('last_n_days'),
            start_date=filters.get('start_date'),
            end_date=filters.get('end_date')
        )
    
    return branch_df

@router.get("/{branch_id}")
async def get_branch_details(branch_id: str):
    """Get branch details."""
    df = get_df()
    
    branch_data = df[df['branch_admin_id'] == branch_id]
    if branch_data.empty:
        raise HTTPException(status_code=404, detail=f"Branch {branch_id} not found")
    
    # Get the first row to extract branch details
    first_row = branch_data.iloc[0]
    
    return {
        "branch_admin_id": branch_id,
        "branch_name": first_row.get('branch_name', f'Branch {branch_id}'),
        "merchant_id": first_row.get('merchant_id'),
        "merchant_name": first_row.get('merchant_name')
    }

@router.get("/{branch_id}/stats")
async def get_branch_stats(
    branch_id: str,
    start_date: Optional[str] = Query(None),
    end_date: Optional[str] = Query(None),
    year: Optional[int] = Query(None),
    month: Optional[int] = Query(None),
    last_n_days: Optional[int] = Query(None)
):
    """Get branch statistics."""
    filters = {
        'start_date': start_date,
        'end_date': end_date,
        'year': year,
        'month': month,
        'last_n_days': last_n_days
    }
    
    # Remove None values
    filters = {k: v for k, v in filters.items() if v is not None}
    
    df = get_branch_data(branch_id, filters)
    
    # Calculate basic statistics
    total_transactions = len(df)
    total_value = df['amount'].sum()
    avg_value = df['amount'].mean()
    max_value = df['amount'].max()
    min_value = df['amount'].min()
    unique_customers = df['customer_id'].nunique()
    unique_terminals = df['terminal_id'].nunique()
    
    stats = [
        SimpleStat(metric="Total Transactions", value=total_transactions),
        SimpleStat(metric="Total Transaction Value", value=round(total_value, 2)),
        SimpleStat(metric="Average Transaction Value", value=round(avg_value, 2)),
        SimpleStat(metric="Max Transaction Value", value=round(max_value, 2)),
        SimpleStat(metric="Min Transaction Value", value=round(min_value, 2)),
        SimpleStat(metric="Total Customers", value=unique_customers),
        SimpleStat(metric="Total Terminals", value=unique_terminals),
    ]
    
    return stats

@router.get("/{branch_id}/overview")
async def get_branch_overview(
    branch_id: str,
    granularity: str = Query("monthly", regex="^(daily|weekly|monthly|yearly)$"),
    top_mode: str = Query("amount", regex="^(amount|count)$"),
    top_limit: int = Query(10, ge=1, le=100),
    start_date: Optional[str] = Query(None),
    end_date: Optional[str] = Query(None),
    year: Optional[int] = Query(None),
    month: Optional[int] = Query(None),
    last_n_days: Optional[int] = Query(None)
):
    """Get comprehensive branch overview data."""
    filters = {
        'start_date': start_date,
        'end_date': end_date,
        'year': year,
        'month': month,
        'last_n_days': last_n_days
    }
    
    # Remove None values
    filters = {k: v for k, v in filters.items() if v is not None}
    
    df = get_branch_data(branch_id, filters)
    
    # Get all the overview data
    transaction_volume = branch_admins.get_transaction_volume_over_time(df, granularity, filters)
    transaction_count = branch_admins.get_transaction_count_over_time(df, granularity, filters)
    average_transactions = branch_admins.get_average_transaction_over_time(df, granularity, filters)
    segmentation = branch_admins.get_customer_segmentation(df, filters)
    top_customers = branch_admins.get_top_customers(df, top_mode, top_limit, filters)
    transaction_outliers = branch_admins.get_transaction_outliers(df, filters)
    days_between_transactions = branch_admins.get_days_between_transactions(df, filters)
    
    return {
        "transaction_volume": transaction_volume,
        "transaction_count": transaction_count,
        "average_transactions": average_transactions,
        "segmentation": segmentation,
        "top_customers": top_customers,
        "transaction_outliers": transaction_outliers,
        "days_between_transactions": days_between_transactions
    }

@router.get("/{branch_id}/transaction-volume")
async def get_branch_transaction_volume(
    branch_id: str,
    granularity: str = Query("monthly", regex="^(daily|weekly|monthly|yearly)$"),
    start_date: Optional[str] = Query(None),
    end_date: Optional[str] = Query(None),
    year: Optional[int] = Query(None),
    month: Optional[int] = Query(None),
    last_n_days: Optional[int] = Query(None)
):
    """Get branch transaction volume over time."""
    filters = {
        'start_date': start_date,
        'end_date': end_date,
        'year': year,
        'month': month,
        'last_n_days': last_n_days
    }
    
    # Remove None values
    filters = {k: v for k, v in filters.items() if v is not None}
    
    df = get_branch_data(branch_id, filters)
    
    return branch_admins.get_transaction_volume_over_time(df, granularity, filters)

@router.get("/{branch_id}/transaction-count")
async def get_branch_transaction_count(
    branch_id: str,
    granularity: str = Query("monthly", regex="^(daily|weekly|monthly|yearly)$"),
    start_date: Optional[str] = Query(None),
    end_date: Optional[str] = Query(None),
    year: Optional[int] = Query(None),
    month: Optional[int] = Query(None),
    last_n_days: Optional[int] = Query(None)
):
    """Get branch transaction count over time."""
    filters = {
        'start_date': start_date,
        'end_date': end_date,
        'year': year,
        'month': month,
        'last_n_days': last_n_days
    }
    
    # Remove None values
    filters = {k: v for k, v in filters.items() if v is not None}
    
    df = get_branch_data(branch_id, filters)
    
    return branch_admins.get_transaction_count_over_time(df, granularity, filters)

@router.get("/{branch_id}/top-customers")
async def get_branch_top_customers(
    branch_id: str,
    mode: str = Query("amount", regex="^(amount|count)$"),
    limit: int = Query(10, ge=1, le=100),
    start_date: Optional[str] = Query(None),
    end_date: Optional[str] = Query(None),
    year: Optional[int] = Query(None),
    month: Optional[int] = Query(None),
    last_n_days: Optional[int] = Query(None)
):
    """Get top customers for a branch."""
    filters = {
        'start_date': start_date,
        'end_date': end_date,
        'year': year,
        'month': month,
        'last_n_days': last_n_days
    }
    
    # Remove None values
    filters = {k: v for k, v in filters.items() if v is not None}
    
    df = get_branch_data(branch_id, filters)
    
    return branch_admins.get_top_customers(df, mode, limit, filters)

@router.get("/{branch_id}/top-terminals")
async def get_branch_top_terminals(
    branch_id: str,
    mode: str = Query("amount", regex="^(amount|count)$"),
    limit: int = Query(10, ge=1, le=100),
    start_date: Optional[str] = Query(None),
    end_date: Optional[str] = Query(None),
    year: Optional[int] = Query(None),
    month: Optional[int] = Query(None),
    last_n_days: Optional[int] = Query(None)
):
    """Get top terminals for a branch."""
    filters = {
        'start_date': start_date,
        'end_date': end_date,
        'year': year,
        'month': month,
        'last_n_days': last_n_days
    }
    
    # Remove None values
    filters = {k: v for k, v in filters.items() if v is not None}
    
    df = get_branch_data(branch_id, filters)
    
    # Calculate top terminals by amount or count
    if mode == "amount":
        grouped = df.groupby('terminal_id').agg({
            'amount': ['sum', 'count']
        }).reset_index()
        grouped.columns = ['terminal_id', 'total_amount', 'transaction_count']
        sorted_data = grouped.sort_values('total_amount', ascending=False).head(limit)
        metric = f"Top {limit} Terminals by Amount"
    else:  # mode == "count"
        grouped = df.groupby('terminal_id').agg({
            'amount': ['sum', 'count']
        }).reset_index()
        grouped.columns = ['terminal_id', 'total_amount', 'transaction_count']
        sorted_data = grouped.sort_values('transaction_count', ascending=False).head(limit)
        metric = f"Top {limit} Terminals by Transaction Count"
    
    return TableData(
        metric=metric,
        data=sorted_data.to_dict(orient="records")
    )

@router.get("/{branch_id}/terminals")
async def get_branch_terminals(branch_id: str):
    """Get all terminals for a branch."""
    df = get_df()
    
    branch_terminals = df[df['branch_admin_id'] == branch_id]['terminal_id'].unique()
    
    if len(branch_terminals) == 0:
        raise HTTPException(status_code=404, detail=f"No terminals found for branch {branch_id}")
    
    # Get terminal details
    terminals = []
    for terminal_id in branch_terminals:
        terminal_data = df[df['terminal_id'] == terminal_id].iloc[0]
        terminals.append({
            "terminal_id": terminal_id,
            "branch_admin_id": branch_id,
            "branch_name": terminal_data.get('branch_name'),
            "merchant_id": terminal_data.get('merchant_id'),
            "merchant_name": terminal_data.get('merchant_name')
        })
    
    return terminals

@router.get("/{branch_id}/terminal-activity-heatmap")
async def get_branch_terminal_activity_heatmap(
    branch_id: str,
    granularity: str = Query("monthly", regex="^(daily|weekly|monthly|yearly)$"),
    start_date: Optional[str] = Query(None),
    end_date: Optional[str] = Query(None),
    year: Optional[int] = Query(None),
    month: Optional[int] = Query(None),
    last_n_days: Optional[int] = Query(None)
):
    """Get terminal activity heatmap for a branch."""
    filters = {
        'start_date': start_date,
        'end_date': end_date,
        'year': year,
        'month': month,
        'last_n_days': last_n_days
    }

    # Remove None values
    filters = {k: v for k, v in filters.items() if v is not None}

    df = get_branch_data(branch_id, filters)

    # Get terminal activity heatmap similar to merchant heatmap
    from app.logic.agents import get_terminal_activity_heatmap

    try:
        return get_terminal_activity_heatmap(df, granularity, filters)
    except Exception as e:
        # Fallback to basic structure if heatmap function doesn't exist
        return {
            "metric": f"Terminal Activity Heatmap for Branch {branch_id}",
            "periods": [],
            "transaction_volume": [],
            "transaction_count": [],
            "average_transaction_value": []
        }
